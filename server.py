#!/usr/bin/env python3
"""
Simple HTTP server for testing the Rubik's Cube application
Serves files with proper MIME types for ES6 modules
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler that sets proper MIME types for JavaScript modules"""
    
    def end_headers(self):
        # Set proper MIME type for JavaScript modules
        if self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')
        super().end_headers()
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def start_server(port=8000, directory=None):
    """Start the HTTP server"""
    
    if directory:
        os.chdir(directory)
    
    # Get the current directory
    current_dir = os.getcwd()
    print(f"📁 Serving files from: {current_dir}")
    
    # Create server
    with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
        print(f"🌐 Server running at http://localhost:{port}/")
        print(f"🎲 Open http://localhost:{port}/test-modular.html to test the modular architecture")
        print(f"🎲 Open http://localhost:{port}/index.html to test the original version")
        print("📝 Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
            httpd.shutdown()

if __name__ == "__main__":
    # Parse command line arguments
    port = 8000
    directory = None
    
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    if len(sys.argv) > 2:
        directory = sys.argv[2]
        if not os.path.exists(directory):
            print(f"Directory '{directory}' does not exist. Using current directory.")
            directory = None
    
    start_server(port, directory)
