/**
 * MoveEngine.js - <PERSON>les move animations and execution
 * Manages the visual animation of cube moves while coordinating with state changes
 */

// THREE is available globally from CDN

export class MoveEngine {
    constructor(cubeRenderer, stateManager) {
        this.cubeRenderer = cubeRenderer;
        this.stateManager = stateManager;
        this.isAnimating = false;
        this.animationQueue = [];
        this.animationSpeed = 300; // Default animation duration in ms
        this.fastAnimationSpeed = 150; // For batch operations
    }

    /**
     * Execute a move with animation
     * @param {string} move - Move notation (R, L, U, D, F, B, with optional ')
     * @param {boolean} fast - Whether to use fast animation
     * @returns {Promise} Promise that resolves when animation completes
     */
    async executeMove(move, fast = false) {
        return new Promise((resolve, reject) => {
            if (!this.stateManager.isValidMove(move)) {
                reject(new Error(`Invalid move: ${move}`));
                return;
            }

            // Add to animation queue
            this.animationQueue.push({
                move,
                fast,
                resolve,
                reject
            });

            // Process queue if not already animating
            if (!this.isAnimating) {
                this.processAnimationQueue();
            }
        });
    }

    /**
     * Execute a sequence of moves
     * @param {string[]} moves - Array of move notations
     * @param {boolean} fast - Whether to use fast animation
     * @returns {Promise} Promise that resolves when all animations complete
     */
    async executeMoveSequence(moves, fast = false) {
        const promises = moves.map(move => this.executeMove(move, fast));
        return Promise.all(promises);
    }

    /**
     * Process the animation queue
     */
    async processAnimationQueue() {
        if (this.animationQueue.length === 0) {
            this.isAnimating = false;
            return;
        }

        this.isAnimating = true;
        const { move, fast, resolve, reject } = this.animationQueue.shift();

        try {
            await this.animateMove(move, fast);
            resolve();
        } catch (error) {
            reject(error);
        }

        // Continue processing queue
        this.processAnimationQueue();
    }

    /**
     * Animate a single move
     * @param {string} move - Move notation
     * @param {boolean} fast - Whether to use fast animation
     * @returns {Promise} Promise that resolves when animation completes
     */
    async animateMove(move, fast = false) {
        return new Promise((resolve, reject) => {
            try {
                // Apply move to state first
                if (!this.stateManager.applyMove(move)) {
                    reject(new Error(`Failed to apply move: ${move}`));
                    return;
                }

                // Get cubelets that belong to this face
                const faceGroup = this.getFaceGroup(move);
                if (faceGroup.length === 0) {
                    reject(new Error(`No cubelets found for move: ${move}`));
                    return;
                }

                // Create temporary group for rotation
                const rotationGroup = new THREE.Group();
                const scene = this.cubeRenderer.getScene();
                const cube = this.cubeRenderer.getCube();
                scene.add(rotationGroup);

                // Move cubelets to rotation group
                faceGroup.forEach(cubelet => {
                    cube.remove(cubelet);
                    rotationGroup.add(cubelet);
                });

                // Calculate rotation parameters
                const { axis, angle } = this.getRotationParameters(move);
                const duration = fast ? this.fastAnimationSpeed : this.animationSpeed;

                // Animate rotation
                this.animateRotation(rotationGroup, axis, angle, duration, () => {
                    // Move cubelets back to main cube group
                    const cubeletsToMove = [...rotationGroup.children];
                    cubeletsToMove.forEach(cubelet => {
                        // Get world transform
                        const worldPosition = new THREE.Vector3();
                        const worldQuaternion = new THREE.Quaternion();
                        const worldScale = new THREE.Vector3();
                        cubelet.matrixWorld.decompose(worldPosition, worldQuaternion, worldScale);

                        // Remove from rotation group
                        rotationGroup.remove(cubelet);

                        // Apply world transform as local transform
                        cubelet.position.copy(worldPosition);
                        cubelet.quaternion.copy(worldQuaternion);
                        cubelet.scale.copy(worldScale);

                        // Add back to main cube group
                        cube.add(cubelet);
                    });

                    // Clean up
                    scene.remove(rotationGroup);

                    // Update cubelet positions and visuals
                    this.updateCubeletPositions(faceGroup);
                    this.cubeRenderer.updateCubeVisuals();

                    resolve();
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Get cubelets that belong to a specific face
     * @param {string} move - Move notation
     * @returns {THREE.Mesh[]} Array of cubelets
     */
    getFaceGroup(move) {
        const face = move.charAt(0);
        const cubelets = this.cubeRenderer.getCubelets();

        const faceGroups = {
            'R': cubelets.filter(c => Math.round(c.position.x) === 1),  // Right
            'L': cubelets.filter(c => Math.round(c.position.x) === -1), // Left
            'U': cubelets.filter(c => Math.round(c.position.y) === 1),  // Up
            'D': cubelets.filter(c => Math.round(c.position.y) === -1), // Down
            'F': cubelets.filter(c => Math.round(c.position.z) === 1),  // Front
            'B': cubelets.filter(c => Math.round(c.position.z) === -1)  // Back
        };

        return faceGroups[face] || [];
    }

    /**
     * Get rotation parameters for a move
     * @param {string} move - Move notation
     * @returns {Object} Object with axis and angle properties
     */
    getRotationParameters(move) {
        const face = move.charAt(0);
        const isPrime = move.includes("'");
        const isDouble = move.includes('2');

        // Define rotation axes
        const axes = {
            'R': new THREE.Vector3(1, 0, 0),   // X-axis
            'L': new THREE.Vector3(1, 0, 0),   // X-axis
            'U': new THREE.Vector3(0, 1, 0),   // Y-axis
            'D': new THREE.Vector3(0, 1, 0),   // Y-axis
            'F': new THREE.Vector3(0, 0, 1),   // Z-axis
            'B': new THREE.Vector3(0, 0, 1)    // Z-axis
        };

        // Calculate angle based on face and direction
        let angle;
        if (isDouble) {
            angle = Math.PI; // 180 degrees
        } else {
            switch (face) {
                case 'R':
                    angle = isPrime ? Math.PI / 2 : -Math.PI / 2;
                    break;
                case 'L':
                    angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
                    break;
                case 'U':
                    angle = isPrime ? Math.PI / 2 : -Math.PI / 2;
                    break;
                case 'D':
                    angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
                    break;
                case 'F':
                    angle = isPrime ? Math.PI / 2 : -Math.PI / 2;
                    break;
                case 'B':
                    angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
                    break;
                default:
                    angle = isPrime ? -Math.PI / 2 : Math.PI / 2;
            }
        }

        return {
            axis: axes[face] || new THREE.Vector3(0, 1, 0),
            angle: angle
        };
    }

    /**
     * Animate rotation of a group
     * @param {THREE.Group} group - Group to rotate
     * @param {THREE.Vector3} axis - Rotation axis
     * @param {number} targetAngle - Target rotation angle
     * @param {number} duration - Animation duration in ms
     * @param {Function} onComplete - Callback when animation completes
     */
    animateRotation(group, axis, targetAngle, duration, onComplete) {
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const eased = this.easeInOutCubic(progress);
            const currentAngle = targetAngle * eased;

            // Reset rotation and apply new rotation
            group.rotation.set(0, 0, 0);
            group.rotateOnAxis(axis, currentAngle);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                onComplete();
            }
        };

        animate();
    }

    /**
     * Easing function for smooth animations
     * @param {number} t - Progress value (0-1)
     * @returns {number} Eased value
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    /**
     * Update cubelet positions after rotation
     * @param {THREE.Mesh[]} cubelets - Cubelets to update
     */
    updateCubeletPositions(cubelets) {
        cubelets.forEach(cubelet => {
            const pos = cubelet.position;

            // Round positions to avoid floating point errors
            pos.x = Math.round(pos.x);
            pos.y = Math.round(pos.y);
            pos.z = Math.round(pos.z);

            // Update userData indices
            cubelet.userData.x = pos.x + 1; // Convert from -1,0,1 to 0,1,2
            cubelet.userData.y = pos.y + 1;
            cubelet.userData.z = pos.z + 1;
        });
    }

    /**
     * Check if currently animating
     * @returns {boolean} True if animating
     */
    getIsAnimating() {
        return this.isAnimating;
    }

    /**
     * Clear animation queue
     */
    clearQueue() {
        this.animationQueue = [];
    }

    /**
     * Set animation speed
     * @param {number} speed - Animation duration in ms
     */
    setAnimationSpeed(speed) {
        this.animationSpeed = Math.max(50, speed); // Minimum 50ms
    }

    /**
     * Set fast animation speed
     * @param {number} speed - Fast animation duration in ms
     */
    setFastAnimationSpeed(speed) {
        this.fastAnimationSpeed = Math.max(25, speed); // Minimum 25ms
    }
}
