/**
 * CubeState.js - Robust cube state representation and management
 * Handles the logical state of the Rubik's cube independently of rendering
 */

export class CubeState {
    constructor() {
        // Standard cube colors - matches your existing color scheme
        this.colors = {
            white: 0xffffff,   // Top
            yellow: 0xffff00,  // Bottom  
            red: 0xff0000,     // Right
            orange: 0xff8800,  // Left
            green: 0x00ff00,   // Back
            blue: 0x0000ff,    // Front
            dark: 0x333333     // Hidden faces
        };

        // Color name mapping for easy lookup
        this.colorNames = {
            [this.colors.white]: 'white',
            [this.colors.yellow]: 'yellow', 
            [this.colors.red]: 'red',
            [this.colors.orange]: 'orange',
            [this.colors.green]: 'green',
            [this.colors.blue]: 'blue',
            [this.colors.dark]: 'dark'
        };

        // Initialize solved state
        this.reset();
    }

    /**
     * Reset cube to solved state
     * Standard orientation: Green front, White top, Red right, Orange left, Blue back, Yellow bottom
     */
    reset() {
        // Each face is represented as a 3x3 array (9 stickers)
        // Stickers are ordered: top-left to bottom-right, row by row
        this.faces = {
            front: Array(9).fill('green'),    // Green face (z = +1)
            back: Array(9).fill('blue'),      // Blue face (z = -1)  
            right: Array(9).fill('red'),      // Red face (x = +1)
            left: Array(9).fill('orange'),    // Orange face (x = -1)
            top: Array(9).fill('white'),      // White face (y = +1)
            bottom: Array(9).fill('yellow')   // Yellow face (y = -1)
        };

        // Track cube positions for each cubelet (3x3x3 = 27 cubelets)
        this.positions = [];
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                for (let z = 0; z < 3; z++) {
                    this.positions.push({ x, y, z });
                }
            }
        }
    }

    /**
     * Get the color of a specific sticker on a face
     * @param {string} face - Face name (front, back, right, left, top, bottom)
     * @param {number} index - Sticker index (0-8)
     * @returns {string} Color name
     */
    getSticker(face, index) {
        if (!this.faces[face] || index < 0 || index > 8) {
            throw new Error(`Invalid face "${face}" or index ${index}`);
        }
        return this.faces[face][index];
    }

    /**
     * Set the color of a specific sticker on a face
     * @param {string} face - Face name
     * @param {number} index - Sticker index (0-8)  
     * @param {string} color - Color name
     */
    setSticker(face, index, color) {
        if (!this.faces[face] || index < 0 || index > 8) {
            throw new Error(`Invalid face "${face}" or index ${index}`);
        }
        if (!Object.values(this.colorNames).includes(color)) {
            throw new Error(`Invalid color "${color}"`);
        }
        this.faces[face][index] = color;
    }

    /**
     * Get all stickers for a face as an array
     * @param {string} face - Face name
     * @returns {string[]} Array of 9 color names
     */
    getFace(face) {
        if (!this.faces[face]) {
            throw new Error(`Invalid face "${face}"`);
        }
        return [...this.faces[face]]; // Return copy to prevent external modification
    }

    /**
     * Set all stickers for a face
     * @param {string} face - Face name
     * @param {string[]} colors - Array of 9 color names
     */
    setFace(face, colors) {
        if (!this.faces[face]) {
            throw new Error(`Invalid face "${face}"`);
        }
        if (!Array.isArray(colors) || colors.length !== 9) {
            throw new Error('Colors must be an array of 9 elements');
        }
        this.faces[face] = [...colors];
    }

    /**
     * Check if the cube is in solved state
     * @returns {boolean} True if solved
     */
    isSolved() {
        const expectedFaces = {
            front: 'green',
            back: 'blue', 
            right: 'red',
            left: 'orange',
            top: 'white',
            bottom: 'yellow'
        };

        for (const [face, expectedColor] of Object.entries(expectedFaces)) {
            if (!this.faces[face].every(color => color === expectedColor)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get a compact string representation of the cube state
     * @returns {string} State string
     */
    getStateString() {
        const faceOrder = ['front', 'back', 'right', 'left', 'top', 'bottom'];
        const colorCodes = {
            'white': 'W', 'yellow': 'Y', 'red': 'R', 
            'orange': 'O', 'green': 'G', 'blue': 'B', 'dark': 'X'
        };

        return faceOrder.map(face => 
            this.faces[face].map(color => colorCodes[color] || 'X').join('')
        ).join('|');
    }

    /**
     * Create a deep copy of the current state
     * @returns {CubeState} New CubeState instance with copied data
     */
    clone() {
        const newState = new CubeState();
        for (const [face, colors] of Object.entries(this.faces)) {
            newState.faces[face] = [...colors];
        }
        newState.positions = this.positions.map(pos => ({ ...pos }));
        return newState;
    }

    /**
     * Load state from a serialized object
     * @param {Object} stateData - Serialized state data
     */
    loadFromData(stateData) {
        if (!stateData || !stateData.faces) {
            throw new Error('Invalid state data');
        }

        // Validate and load faces
        const requiredFaces = ['front', 'back', 'right', 'left', 'top', 'bottom'];
        for (const face of requiredFaces) {
            if (!stateData.faces[face] || !Array.isArray(stateData.faces[face]) || 
                stateData.faces[face].length !== 9) {
                throw new Error(`Invalid face data for "${face}"`);
            }
            this.faces[face] = [...stateData.faces[face]];
        }

        // Load positions if available
        if (stateData.positions && Array.isArray(stateData.positions)) {
            this.positions = stateData.positions.map(pos => ({ ...pos }));
        }
    }

    /**
     * Export state to a serializable object
     * @returns {Object} Serializable state data
     */
    exportData() {
        return {
            faces: {
                front: [...this.faces.front],
                back: [...this.faces.back],
                right: [...this.faces.right], 
                left: [...this.faces.left],
                top: [...this.faces.top],
                bottom: [...this.faces.bottom]
            },
            positions: this.positions.map(pos => ({ ...pos })),
            metadata: {
                isSolved: this.isSolved(),
                stateString: this.getStateString(),
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * Validate the current state for consistency
     * @returns {Object} Validation result with isValid flag and errors array
     */
    validate() {
        const errors = [];
        const colorCounts = {};

        // Count colors across all faces
        for (const [faceName, faceColors] of Object.entries(this.faces)) {
            if (!Array.isArray(faceColors) || faceColors.length !== 9) {
                errors.push(`Face "${faceName}" must have exactly 9 stickers`);
                continue;
            }

            for (const color of faceColors) {
                if (!Object.values(this.colorNames).includes(color)) {
                    errors.push(`Invalid color "${color}" on face "${faceName}"`);
                    continue;
                }
                colorCounts[color] = (colorCounts[color] || 0) + 1;
            }
        }

        // Check color distribution (each color should appear exactly 9 times, except 'dark')
        const expectedColors = ['white', 'yellow', 'red', 'orange', 'green', 'blue'];
        for (const color of expectedColors) {
            const count = colorCounts[color] || 0;
            if (count !== 9) {
                errors.push(`Color "${color}" appears ${count} times, expected 9`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}
