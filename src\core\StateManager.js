/**
 * StateManager.js - Manages cube state transitions and move applications
 * Handles the logical application of moves to the cube state
 */

import { CubeState } from './CubeState.js';

export class StateManager {
    constructor() {
        this.currentState = new CubeState();
        this.moveHistory = [];
        this.stateHistory = [];
        this.maxHistorySize = 1000; // Limit history to prevent memory issues
    }

    /**
     * Get the current cube state
     * @returns {CubeState} Current state
     */
    getCurrentState() {
        return this.currentState;
    }

    /**
     * Reset cube to solved state
     */
    reset() {
        this.currentState.reset();
        this.moveHistory = [];
        this.stateHistory = [];
    }

    /**
     * Apply a move to the current state
     * @param {string} move - Move notation (R, L, U, D, F, B, with optional ')
     * @returns {boolean} Success status
     */
    applyMove(move) {
        if (!this.isValidMove(move)) {
            console.error(`Invalid move: ${move}`);
            return false;
        }

        // Save current state to history
        this.saveStateToHistory();

        // Apply the move
        const success = this.executeMove(move);
        
        if (success) {
            this.moveHistory.push(move);
            // Limit history size
            if (this.moveHistory.length > this.maxHistorySize) {
                this.moveHistory.shift();
                this.stateHistory.shift();
            }
        }

        return success;
    }

    /**
     * Apply a sequence of moves
     * @param {string[]} moves - Array of move notations
     * @returns {boolean} Success status
     */
    applyMoveSequence(moves) {
        for (const move of moves) {
            if (!this.applyMove(move)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Undo the last move
     * @returns {boolean} Success status
     */
    undoLastMove() {
        if (this.stateHistory.length === 0) {
            return false;
        }

        // Restore previous state
        const previousState = this.stateHistory.pop();
        this.currentState.loadFromData(previousState);
        this.moveHistory.pop();
        
        return true;
    }

    /**
     * Execute a single move on the cube state
     * @param {string} move - Move notation
     * @returns {boolean} Success status
     */
    executeMove(move) {
        const face = move.charAt(0);
        const isPrime = move.includes("'");
        const isDouble = move.includes('2');

        try {
            if (isDouble) {
                // Double move = two 90-degree rotations
                this.rotateFace(face, false);
                this.rotateFace(face, false);
            } else {
                this.rotateFace(face, isPrime);
            }
            return true;
        } catch (error) {
            console.error(`Error executing move ${move}:`, error);
            return false;
        }
    }

    /**
     * Rotate a face of the cube
     * @param {string} face - Face to rotate (R, L, U, D, F, B)
     * @param {boolean} isPrime - Whether it's a prime (counterclockwise) rotation
     */
    rotateFace(face, isPrime) {
        // First rotate the face itself
        this.rotateFaceStickers(face, isPrime);
        
        // Then rotate the adjacent edge stickers
        this.rotateAdjacentEdges(face, isPrime);
    }

    /**
     * Rotate the stickers on a face itself (not the adjacent edges)
     * @param {string} face - Face to rotate
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateFaceStickers(face, isPrime) {
        const faceMap = {
            'R': 'right', 'L': 'left', 'U': 'top', 
            'D': 'bottom', 'F': 'front', 'B': 'back'
        };
        
        const faceName = faceMap[face];
        if (!faceName) {
            throw new Error(`Invalid face: ${face}`);
        }

        const faceStickers = this.currentState.getFace(faceName);
        const rotatedStickers = this.rotateArray3x3(faceStickers, isPrime);
        this.currentState.setFace(faceName, rotatedStickers);
    }

    /**
     * Rotate a 3x3 array representing face stickers
     * @param {string[]} array - 9-element array representing 3x3 face
     * @param {boolean} isPrime - Whether it's a counterclockwise rotation
     * @returns {string[]} Rotated array
     */
    rotateArray3x3(array, isPrime) {
        // Convert 1D array to 2D for easier rotation
        const matrix = [
            [array[0], array[1], array[2]],
            [array[3], array[4], array[5]], 
            [array[6], array[7], array[8]]
        ];

        // Rotate matrix
        const rotated = isPrime ? this.rotateMatrixCounterClockwise(matrix) : this.rotateMatrixClockwise(matrix);
        
        // Convert back to 1D array
        return [
            rotated[0][0], rotated[0][1], rotated[0][2],
            rotated[1][0], rotated[1][1], rotated[1][2],
            rotated[2][0], rotated[2][1], rotated[2][2]
        ];
    }

    /**
     * Rotate a 3x3 matrix clockwise
     * @param {Array[]} matrix - 3x3 matrix
     * @returns {Array[]} Rotated matrix
     */
    rotateMatrixClockwise(matrix) {
        return [
            [matrix[2][0], matrix[1][0], matrix[0][0]],
            [matrix[2][1], matrix[1][1], matrix[0][1]],
            [matrix[2][2], matrix[1][2], matrix[0][2]]
        ];
    }

    /**
     * Rotate a 3x3 matrix counterclockwise
     * @param {Array[]} matrix - 3x3 matrix
     * @returns {Array[]} Rotated matrix
     */
    rotateMatrixCounterClockwise(matrix) {
        return [
            [matrix[0][2], matrix[1][2], matrix[2][2]],
            [matrix[0][1], matrix[1][1], matrix[2][1]],
            [matrix[0][0], matrix[1][0], matrix[2][0]]
        ];
    }

    /**
     * Rotate the adjacent edge stickers when a face is rotated
     * @param {string} face - Face being rotated
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateAdjacentEdges(face, isPrime) {
        // Define which edges move for each face rotation
        const edgeRotations = {
            'R': () => this.rotateRightEdges(isPrime),
            'L': () => this.rotateLeftEdges(isPrime),
            'U': () => this.rotateUpEdges(isPrime),
            'D': () => this.rotateDownEdges(isPrime),
            'F': () => this.rotateFrontEdges(isPrime),
            'B': () => this.rotateBackEdges(isPrime)
        };

        const rotateFunction = edgeRotations[face];
        if (rotateFunction) {
            rotateFunction();
        }
    }

    /**
     * Rotate edges for R face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateRightEdges(isPrime) {
        const front = this.currentState.getFace('front');
        const top = this.currentState.getFace('top');
        const back = this.currentState.getFace('back');
        const bottom = this.currentState.getFace('bottom');

        if (isPrime) {
            // R' rotation: front -> bottom -> back -> top -> front
            const temp = [front[2], front[5], front[8]];
            front[2] = top[2]; front[5] = top[5]; front[8] = top[8];
            top[2] = back[0]; top[5] = back[3]; top[8] = back[6];
            back[0] = bottom[2]; back[3] = bottom[5]; back[6] = bottom[8];
            bottom[2] = temp[0]; bottom[5] = temp[1]; bottom[8] = temp[2];
        } else {
            // R rotation: front -> top -> back -> bottom -> front
            const temp = [front[2], front[5], front[8]];
            front[2] = bottom[2]; front[5] = bottom[5]; front[8] = bottom[8];
            bottom[2] = back[6]; bottom[5] = back[3]; bottom[8] = back[0];
            back[6] = top[8]; back[3] = top[5]; back[0] = top[2];
            top[8] = temp[2]; top[5] = temp[1]; top[2] = temp[0];
        }

        this.currentState.setFace('front', front);
        this.currentState.setFace('top', top);
        this.currentState.setFace('back', back);
        this.currentState.setFace('bottom', bottom);
    }

    /**
     * Rotate edges for L face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateLeftEdges(isPrime) {
        const front = this.currentState.getFace('front');
        const top = this.currentState.getFace('top');
        const back = this.currentState.getFace('back');
        const bottom = this.currentState.getFace('bottom');

        if (isPrime) {
            // L' rotation: front -> top -> back -> bottom -> front
            const temp = [front[0], front[3], front[6]];
            front[0] = bottom[0]; front[3] = bottom[3]; front[6] = bottom[6];
            bottom[0] = back[8]; bottom[3] = back[5]; bottom[6] = back[2];
            back[8] = top[0]; back[5] = top[3]; back[2] = top[6];
            top[0] = temp[0]; top[3] = temp[1]; top[6] = temp[2];
        } else {
            // L rotation: front -> bottom -> back -> top -> front
            const temp = [front[0], front[3], front[6]];
            front[0] = top[0]; front[3] = top[3]; front[6] = top[6];
            top[0] = back[8]; top[3] = back[5]; top[6] = back[2];
            back[8] = bottom[0]; back[5] = bottom[3]; back[2] = bottom[6];
            bottom[0] = temp[0]; bottom[3] = temp[1]; bottom[6] = temp[2];
        }

        this.currentState.setFace('front', front);
        this.currentState.setFace('top', top);
        this.currentState.setFace('back', back);
        this.currentState.setFace('bottom', bottom);
    }

    /**
     * Rotate edges for U face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateUpEdges(isPrime) {
        const front = this.currentState.getFace('front');
        const right = this.currentState.getFace('right');
        const back = this.currentState.getFace('back');
        const left = this.currentState.getFace('left');

        if (isPrime) {
            // U' rotation: front -> left -> back -> right -> front
            const temp = [front[0], front[1], front[2]];
            front[0] = right[0]; front[1] = right[1]; front[2] = right[2];
            right[0] = back[0]; right[1] = back[1]; right[2] = back[2];
            back[0] = left[0]; back[1] = left[1]; back[2] = left[2];
            left[0] = temp[0]; left[1] = temp[1]; left[2] = temp[2];
        } else {
            // U rotation: front -> right -> back -> left -> front
            const temp = [front[0], front[1], front[2]];
            front[0] = left[0]; front[1] = left[1]; front[2] = left[2];
            left[0] = back[0]; left[1] = back[1]; left[2] = back[2];
            back[0] = right[0]; back[1] = right[1]; back[2] = right[2];
            right[0] = temp[0]; right[1] = temp[1]; right[2] = temp[2];
        }

        this.currentState.setFace('front', front);
        this.currentState.setFace('right', right);
        this.currentState.setFace('back', back);
        this.currentState.setFace('left', left);
    }

    /**
     * Rotate edges for D face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateDownEdges(isPrime) {
        const front = this.currentState.getFace('front');
        const right = this.currentState.getFace('right');
        const back = this.currentState.getFace('back');
        const left = this.currentState.getFace('left');

        if (isPrime) {
            // D' rotation: front -> right -> back -> left -> front
            const temp = [front[6], front[7], front[8]];
            front[6] = left[6]; front[7] = left[7]; front[8] = left[8];
            left[6] = back[6]; left[7] = back[7]; left[8] = back[8];
            back[6] = right[6]; back[7] = right[7]; back[8] = right[8];
            right[6] = temp[0]; right[7] = temp[1]; right[8] = temp[2];
        } else {
            // D rotation: front -> left -> back -> right -> front
            const temp = [front[6], front[7], front[8]];
            front[6] = right[6]; front[7] = right[7]; front[8] = right[8];
            right[6] = back[6]; right[7] = back[7]; right[8] = back[8];
            back[6] = left[6]; back[7] = left[7]; back[8] = left[8];
            left[6] = temp[0]; left[7] = temp[1]; left[8] = temp[2];
        }

        this.currentState.setFace('front', front);
        this.currentState.setFace('right', right);
        this.currentState.setFace('back', back);
        this.currentState.setFace('left', left);
    }

    /**
     * Rotate edges for F face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateFrontEdges(isPrime) {
        const top = this.currentState.getFace('top');
        const right = this.currentState.getFace('right');
        const bottom = this.currentState.getFace('bottom');
        const left = this.currentState.getFace('left');

        if (isPrime) {
            // F' rotation: top -> left -> bottom -> right -> top
            const temp = [top[6], top[7], top[8]];
            top[6] = right[0]; top[7] = right[3]; top[8] = right[6];
            right[0] = bottom[2]; right[3] = bottom[1]; right[6] = bottom[0];
            bottom[2] = left[8]; bottom[1] = left[5]; bottom[0] = left[2];
            left[8] = temp[0]; left[5] = temp[1]; left[2] = temp[2];
        } else {
            // F rotation: top -> right -> bottom -> left -> top
            const temp = [top[6], top[7], top[8]];
            top[6] = left[8]; top[7] = left[5]; top[8] = left[2];
            left[8] = bottom[2]; left[5] = bottom[1]; left[2] = bottom[0];
            bottom[2] = right[0]; bottom[1] = right[3]; bottom[0] = right[6];
            right[0] = temp[0]; right[3] = temp[1]; right[6] = temp[2];
        }

        this.currentState.setFace('top', top);
        this.currentState.setFace('right', right);
        this.currentState.setFace('bottom', bottom);
        this.currentState.setFace('left', left);
    }

    /**
     * Rotate edges for B face rotation
     * @param {boolean} isPrime - Whether it's a prime rotation
     */
    rotateBackEdges(isPrime) {
        const top = this.currentState.getFace('top');
        const right = this.currentState.getFace('right');
        const bottom = this.currentState.getFace('bottom');
        const left = this.currentState.getFace('left');

        if (isPrime) {
            // B' rotation: top -> right -> bottom -> left -> top
            const temp = [top[0], top[1], top[2]];
            top[0] = left[6]; top[1] = left[3]; top[2] = left[0];
            left[6] = bottom[8]; left[3] = bottom[7]; left[0] = bottom[6];
            bottom[8] = right[2]; bottom[7] = right[5]; bottom[6] = right[8];
            right[2] = temp[0]; right[5] = temp[1]; right[8] = temp[2];
        } else {
            // B rotation: top -> left -> bottom -> right -> top
            const temp = [top[0], top[1], top[2]];
            top[0] = right[2]; top[1] = right[5]; top[2] = right[8];
            right[2] = bottom[8]; right[5] = bottom[7]; right[8] = bottom[6];
            bottom[8] = left[6]; bottom[7] = left[3]; bottom[6] = left[0];
            left[6] = temp[0]; left[3] = temp[1]; left[0] = temp[2];
        }

        this.currentState.setFace('top', top);
        this.currentState.setFace('right', right);
        this.currentState.setFace('bottom', bottom);
        this.currentState.setFace('left', left);
    }

    /**
     * Validate if a move string is valid
     * @param {string} move - Move to validate
     * @returns {boolean} Whether the move is valid
     */
    isValidMove(move) {
        return /^[RLUDFB]'?2?$/.test(move);
    }

    /**
     * Parse a formula string into individual moves
     * @param {string} formula - Formula string (e.g., "R U R' U'")
     * @returns {string[]} Array of individual moves
     */
    parseFormula(formula) {
        if (!formula || typeof formula !== 'string') {
            return [];
        }

        const tokens = formula.replace(/\s+/g, ' ').trim().split(' ');
        const moves = [];

        for (const token of tokens) {
            if (this.isValidMove(token)) {
                moves.push(token);
            } else {
                console.error(`Invalid move in formula: ${token}`);
                return []; // Return empty array for invalid formula
            }
        }

        return moves;
    }

    /**
     * Save current state to history
     */
    saveStateToHistory() {
        this.stateHistory.push(this.currentState.exportData());
    }

    /**
     * Get move history
     * @returns {string[]} Array of moves
     */
    getMoveHistory() {
        return [...this.moveHistory];
    }

    /**
     * Get number of moves from solved state
     * @returns {number} Move count
     */
    getMoveCount() {
        return this.moveHistory.length;
    }

    /**
     * Generate a random scramble
     * @param {number} length - Number of moves in scramble
     * @returns {string[]} Array of scramble moves
     */
    generateScramble(length = 20) {
        const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const scramble = [];

        for (let i = 0; i < length; i++) {
            const move = moves[Math.floor(Math.random() * moves.length)];
            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(move + modifier);
        }

        return scramble;
    }
}
