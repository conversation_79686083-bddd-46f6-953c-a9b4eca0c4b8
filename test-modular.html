<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Modular Rubik's Cube</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
        .test-success { color: #4CAF50; }
        .test-error { color: #f44336; }
        .test-warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="test-info" id="test-info">
        <div>🧪 Testing Modular Architecture...</div>
        <div id="test-results"></div>
    </div>

    <div class="container">
        <header>
            <h1>🧪 Modular Architecture Test</h1>
            <p>Testing the refactored Rubik's Cube application</p>
        </header>

        <div class="main-content">
            <!-- 3D Canvas Container -->
            <div class="canvas-container">
                <canvas id="cube-canvas"></canvas>
                <div class="canvas-overlay">
                    <div class="camera-info">
                        <span id="camera-position">Camera: Loading...</span>
                    </div>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <div class="control-section">
                    <h3>Quick Test Controls</h3>
                    <div class="button-group">
                        <button id="test-scramble" class="btn btn-primary">Test Scramble</button>
                        <button id="test-reset" class="btn btn-secondary">Test Reset</button>
                        <button id="test-move" class="btn btn-success">Test Move (R)</button>
                        <button id="test-formula" class="btn btn-warning">Test Formula (R U R' U')</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Camera Test</h3>
                    <div class="button-group">
                        <button id="test-camera-front" class="btn btn-camera">Front</button>
                        <button id="test-camera-corner" class="btn btn-camera">Corner</button>
                        <button id="test-zoom-in" class="btn btn-camera">Zoom In</button>
                        <button id="test-zoom-out" class="btn btn-camera">Zoom Out</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Data Generation Test</h3>
                    <div class="button-group">
                        <button id="test-capture" class="btn btn-success">Capture State</button>
                        <button id="test-batch" class="btn btn-primary">Mini Batch (2x2)</button>
                        <button id="test-export" class="btn btn-warning">Export Data</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>Module Status</h3>
                    <div class="state-info">
                        <div class="info-item">
                            <span class="label">Core App:</span>
                            <span id="status-app">Loading...</span>
                        </div>
                        <div class="info-item">
                            <span class="label">State Manager:</span>
                            <span id="status-state">Loading...</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Renderer:</span>
                            <span id="status-renderer">Loading...</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Move Engine:</span>
                            <span id="status-moves">Loading...</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Camera:</span>
                            <span id="status-camera">Loading...</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Data Gen:</span>
                            <span id="status-data">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <span id="status-text">Initializing modular architecture...</span>
            </div>
            <div class="status-item">
                <span id="performance-info">FPS: --</span>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- OrbitControls for camera interaction -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- Bundled Modular Architecture -->
    <script src="main-modular-bundle.js"></script>

    <!-- Test Script -->
    <script>
        // No imports needed - everything is bundled

        let app = null;
        let testResults = [];

        function addTestResult(test, status, message = '') {
            testResults.push({ test, status, message, timestamp: new Date().toISOString() });
            updateTestDisplay();
        }

        function updateTestDisplay() {
            const resultsDiv = document.getElementById('test-results');
            const html = testResults.map(result => {
                const statusClass = result.status === 'pass' ? 'test-success' :
                                  result.status === 'fail' ? 'test-error' : 'test-warning';
                const icon = result.status === 'pass' ? '✅' :
                           result.status === 'fail' ? '❌' : '⚠️';
                return `<div class="${statusClass}">${icon} ${result.test}: ${result.message}</div>`;
            }).join('');
            resultsDiv.innerHTML = html;
        }

        async function runTests() {
            try {
                addTestResult('Initialization', 'info', 'Starting...');

                // Wait for app to be available
                await new Promise(resolve => {
                    const checkApp = () => {
                        if (window.rubiksCubeApp) {
                            app = window.rubiksCubeApp;
                            resolve();
                        } else {
                            setTimeout(checkApp, 100);
                        }
                    };
                    checkApp();
                });

                if (!app) {
                    throw new Error('App not initialized');
                }

                addTestResult('App Creation', 'pass', 'Core app created');
                
                // Test modules
                const state = app.getCurrentState();
                addTestResult('State Manager', state ? 'pass' : 'fail', state ? 'Working' : 'Failed');
                
                // Update status indicators
                document.getElementById('status-app').textContent = '✅ Ready';
                document.getElementById('status-state').textContent = '✅ Ready';
                document.getElementById('status-renderer').textContent = '✅ Ready';
                document.getElementById('status-moves').textContent = '✅ Ready';
                document.getElementById('status-camera').textContent = '✅ Ready';
                document.getElementById('status-data').textContent = '✅ Ready';
                
                // Setup test button handlers
                setupTestHandlers(app);
                
                addTestResult('Module Tests', 'pass', 'All modules loaded');
                addTestResult('Overall', 'pass', 'Modular architecture working!');
                
            } catch (error) {
                console.error('Test failed:', error);
                addTestResult('Initialization', 'fail', error.message);
                
                // Update status indicators to show error
                document.getElementById('status-app').textContent = '❌ Error';
                document.getElementById('status-state').textContent = '❌ Error';
                document.getElementById('status-renderer').textContent = '❌ Error';
                document.getElementById('status-moves').textContent = '❌ Error';
                document.getElementById('status-camera').textContent = '❌ Error';
                document.getElementById('status-data').textContent = '❌ Error';
            }
        }
        
        function setupTestHandlers(app) {
            // Test buttons
            document.getElementById('test-scramble').addEventListener('click', async () => {
                try {
                    await app.scramble(5);
                    addTestResult('Scramble', 'pass', '5-move scramble executed');
                } catch (error) {
                    addTestResult('Scramble', 'fail', error.message);
                }
            });
            
            document.getElementById('test-reset').addEventListener('click', () => {
                try {
                    app.reset();
                    addTestResult('Reset', 'pass', 'Cube reset to solved');
                } catch (error) {
                    addTestResult('Reset', 'fail', error.message);
                }
            });
            
            document.getElementById('test-move').addEventListener('click', async () => {
                try {
                    await app.executeMove('R');
                    addTestResult('Single Move', 'pass', 'R move executed');
                } catch (error) {
                    addTestResult('Single Move', 'fail', error.message);
                }
            });
            
            document.getElementById('test-formula').addEventListener('click', async () => {
                try {
                    await app.executeFormula("R U R' U'");
                    addTestResult('Formula', 'pass', 'Formula executed');
                } catch (error) {
                    addTestResult('Formula', 'fail', error.message);
                }
            });
            
            document.getElementById('test-camera-front').addEventListener('click', async () => {
                try {
                    await app.setCameraPreset('front');
                    addTestResult('Camera', 'pass', 'Front preset set');
                } catch (error) {
                    addTestResult('Camera', 'fail', error.message);
                }
            });
            
            document.getElementById('test-camera-corner').addEventListener('click', async () => {
                try {
                    await app.setCameraPreset('corner');
                    addTestResult('Camera', 'pass', 'Corner preset set');
                } catch (error) {
                    addTestResult('Camera', 'fail', error.message);
                }
            });
            
            document.getElementById('test-zoom-in').addEventListener('click', () => {
                try {
                    app.zoom('in');
                    addTestResult('Zoom', 'pass', 'Zoom in');
                } catch (error) {
                    addTestResult('Zoom', 'fail', error.message);
                }
            });
            
            document.getElementById('test-zoom-out').addEventListener('click', () => {
                try {
                    app.zoom('out');
                    addTestResult('Zoom', 'pass', 'Zoom out');
                } catch (error) {
                    addTestResult('Zoom', 'fail', error.message);
                }
            });
            
            document.getElementById('test-capture').addEventListener('click', () => {
                try {
                    app.captureCurrentState();
                    addTestResult('Capture', 'pass', 'State captured');
                } catch (error) {
                    addTestResult('Capture', 'fail', error.message);
                }
            });
            
            document.getElementById('test-batch').addEventListener('click', async () => {
                try {
                    await app.generateBatch({ stateCount: 2, anglesPerState: 2 });
                    addTestResult('Batch Gen', 'pass', '2x2 batch generated');
                } catch (error) {
                    addTestResult('Batch Gen', 'fail', error.message);
                }
            });
            
            document.getElementById('test-export').addEventListener('click', () => {
                try {
                    if (app.getDataCount() === 0) {
                        addTestResult('Export', 'warning', 'No data to export');
                        return;
                    }
                    app.exportData();
                    addTestResult('Export', 'pass', 'Data exported');
                } catch (error) {
                    addTestResult('Export', 'fail', error.message);
                }
            });
        }
        
        // Start tests when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            // Wait a bit for the bundled app to initialize
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
