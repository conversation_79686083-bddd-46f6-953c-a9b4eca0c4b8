/**
 * DataGenerator.js - <PERSON>les ML training data generation
 * Manages the creation of diverse, labeled datasets for machine learning
 */

export class DataGenerator {
    constructor(cubeRenderer, stateManager, cameraController, moveEngine) {
        this.cubeRenderer = cubeRenderer;
        this.stateManager = stateManager;
        this.cameraController = cameraController;
        this.moveEngine = moveEngine;
        
        this.capturedStates = [];
        this.isGenerating = false;
        this.generationProgress = 0;
        this.onProgressCallback = null;
        this.onStatusCallback = null;
    }

    /**
     * Capture the current cube state with screenshot and metadata
     * @returns {Object} Captured state data
     */
    captureCurrentState() {
        const state = this.stateManager.getCurrentState();
        const cameraInfo = this.cameraController.getCameraInfo();
        const screenshot = this.cubeRenderer.takeScreenshot();
        
        const capturedData = {
            id: Date.now() + Math.random(), // Unique ID
            timestamp: new Date().toISOString(),
            cubeState: state.exportData(),
            cameraInfo: cameraInfo,
            moveHistory: this.stateManager.getMoveHistory(),
            screenshot: screenshot,
            metadata: {
                isSolved: state.isSolved(),
                moveCount: this.stateManager.getMoveCount(),
                stateString: state.getStateString()
            }
        };

        this.capturedStates.push(capturedData);
        return capturedData;
    }

    /**
     * Generate a batch of training data
     * @param {Object} options - Generation options
     * @returns {Promise} Promise that resolves when generation completes
     */
    async generateBatch(options = {}) {
        const {
            stateCount = 10,
            anglesPerState = 4,
            scrambleLength = 20,
            includePresets = true,
            includeRandomAngles = true,
            minCameraDistance = 6,
            maxCameraDistance = 10
        } = options;

        if (this.isGenerating) {
            throw new Error('Data generation already in progress');
        }

        this.isGenerating = true;
        this.generationProgress = 0;
        const totalSamples = stateCount * anglesPerState;
        let currentSample = 0;

        try {
            this.updateStatus('Starting batch generation...');

            for (let stateIndex = 0; stateIndex < stateCount; stateIndex++) {
                // Generate a new cube state
                await this.generateCubeState(scrambleLength);
                
                // Capture from multiple angles
                const cameraPositions = this.generateCameraPositions(
                    anglesPerState, 
                    includePresets, 
                    includeRandomAngles,
                    minCameraDistance,
                    maxCameraDistance
                );

                for (const cameraPos of cameraPositions) {
                    // Set camera position
                    await this.cameraController.setCameraPosition(
                        cameraPos.position,
                        cameraPos.target
                    );

                    // Wait a moment for rendering to settle
                    await this.delay(100);

                    // Capture state
                    this.captureCurrentState();

                    currentSample++;
                    this.generationProgress = (currentSample / totalSamples) * 100;
                    this.updateProgress(this.generationProgress);
                    this.updateStatus(`Generated ${currentSample}/${totalSamples} samples`);
                }
            }

            this.updateStatus(`Batch generation complete! Generated ${totalSamples} samples.`);
            return this.capturedStates.slice(-totalSamples); // Return newly generated samples

        } catch (error) {
            this.updateStatus(`Generation failed: ${error.message}`);
            throw error;
        } finally {
            this.isGenerating = false;
            this.generationProgress = 0;
        }
    }

    /**
     * Generate a diverse cube state
     * @param {number} scrambleLength - Length of scramble sequence
     * @returns {Promise} Promise that resolves when state is generated
     */
    async generateCubeState(scrambleLength = 20) {
        // Reset to solved state
        this.stateManager.reset();
        this.cubeRenderer.updateCubeVisuals();

        // Generate and apply scramble
        const scramble = this.generateSmartScramble(scrambleLength);
        await this.moveEngine.executeMoveSequence(scramble, true); // Fast animation

        return scramble;
    }

    /**
     * Generate a smart scramble that avoids redundant moves
     * @param {number} length - Scramble length
     * @returns {string[]} Array of moves
     */
    generateSmartScramble(length) {
        const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const scramble = [];
        let lastMove = '';
        let lastAxis = '';

        for (let i = 0; i < length; i++) {
            let move, axis;
            
            // Avoid consecutive moves on same face or opposite faces
            do {
                move = moves[Math.floor(Math.random() * moves.length)];
                axis = this.getMoveAxis(move);
            } while (move === lastMove || axis === lastAxis);

            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(move + modifier);
            
            lastMove = move;
            lastAxis = axis;
        }

        return scramble;
    }

    /**
     * Get the axis for a move (to avoid consecutive moves on same axis)
     * @param {string} move - Move letter
     * @returns {string} Axis identifier
     */
    getMoveAxis(move) {
        const axisMap = {
            'R': 'x', 'L': 'x',
            'U': 'y', 'D': 'y', 
            'F': 'z', 'B': 'z'
        };
        return axisMap[move] || '';
    }

    /**
     * Generate camera positions for data capture
     * @param {number} count - Number of positions to generate
     * @param {boolean} includePresets - Whether to include preset positions
     * @param {boolean} includeRandom - Whether to include random positions
     * @param {number} minDistance - Minimum camera distance
     * @param {number} maxDistance - Maximum camera distance
     * @returns {Array} Array of camera position objects
     */
    generateCameraPositions(count, includePresets = true, includeRandom = true, minDistance = 6, maxDistance = 10) {
        const positions = [];

        if (includePresets) {
            // Add some preset positions
            const presets = this.cameraController.getPresets();
            const presetNames = Object.keys(presets);
            const presetCount = Math.min(count, presetNames.length);
            
            for (let i = 0; i < presetCount; i++) {
                const presetName = presetNames[i];
                positions.push({
                    ...presets[presetName],
                    type: 'preset',
                    name: presetName
                });
            }
        }

        if (includeRandom) {
            // Fill remaining with random positions
            const remainingCount = count - positions.length;
            
            for (let i = 0; i < remainingCount; i++) {
                const radius = minDistance + Math.random() * (maxDistance - minDistance);
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.PI * 0.2 + Math.random() * Math.PI * 0.6;

                const x = radius * Math.sin(phi) * Math.cos(theta);
                const y = radius * Math.cos(phi);
                const z = radius * Math.sin(phi) * Math.sin(theta);

                positions.push({
                    position: [x, y, z],
                    target: [0, 0, 0],
                    type: 'random',
                    name: `random_${i}`
                });
            }
        }

        return positions.slice(0, count);
    }

    /**
     * Export captured data in various formats
     * @param {string} format - Export format ('json', 'csv', 'tfrecord')
     * @param {Object} options - Export options
     * @returns {Object} Export result
     */
    exportData(format = 'json', options = {}) {
        if (this.capturedStates.length === 0) {
            throw new Error('No data to export');
        }

        const exportData = {
            metadata: {
                exportDate: new Date().toISOString(),
                totalStates: this.capturedStates.length,
                version: '2.0',
                format: format,
                ...options.metadata
            },
            states: this.capturedStates
        };

        switch (format.toLowerCase()) {
            case 'json':
                return this.exportAsJSON(exportData, options);
            case 'csv':
                return this.exportAsCSV(exportData, options);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    /**
     * Export data as JSON
     * @param {Object} data - Data to export
     * @param {Object} options - Export options
     * @returns {Object} Export result with blob and filename
     */
    exportAsJSON(data, options = {}) {
        const jsonString = JSON.stringify(data, null, options.pretty ? 2 : 0);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const filename = options.filename || `rubiks_cube_data_${Date.now()}.json`;
        
        return { blob, filename, size: blob.size };
    }

    /**
     * Export data as CSV (metadata only, images separate)
     * @param {Object} data - Data to export
     * @param {Object} options - Export options
     * @returns {Object} Export result with blob and filename
     */
    exportAsCSV(data, options = {}) {
        const headers = [
            'id', 'timestamp', 'isSolved', 'moveCount', 'stateString',
            'cameraX', 'cameraY', 'cameraZ', 'targetX', 'targetY', 'targetZ',
            'distance', 'preset'
        ];

        const rows = data.states.map(state => [
            state.id,
            state.timestamp,
            state.metadata.isSolved,
            state.metadata.moveCount,
            state.metadata.stateString,
            state.cameraInfo.position[0],
            state.cameraInfo.position[1],
            state.cameraInfo.position[2],
            state.cameraInfo.target[0],
            state.cameraInfo.target[1],
            state.cameraInfo.target[2],
            state.cameraInfo.distance,
            state.cameraInfo.preset
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const filename = options.filename || `rubiks_cube_metadata_${Date.now()}.csv`;
        
        return { blob, filename, size: blob.size };
    }

    /**
     * Clear all captured data
     */
    clearData() {
        this.capturedStates = [];
    }

    /**
     * Get captured data count
     * @returns {number} Number of captured states
     */
    getDataCount() {
        return this.capturedStates.length;
    }

    /**
     * Get generation progress
     * @returns {number} Progress percentage (0-100)
     */
    getProgress() {
        return this.generationProgress;
    }

    /**
     * Check if currently generating data
     * @returns {boolean} True if generating
     */
    getIsGenerating() {
        return this.isGenerating;
    }

    /**
     * Set progress callback
     * @param {Function} callback - Progress callback function
     */
    setProgressCallback(callback) {
        this.onProgressCallback = callback;
    }

    /**
     * Set status callback
     * @param {Function} callback - Status callback function
     */
    setStatusCallback(callback) {
        this.onStatusCallback = callback;
    }

    /**
     * Update progress
     * @param {number} progress - Progress percentage
     */
    updateProgress(progress) {
        if (this.onProgressCallback) {
            this.onProgressCallback(progress);
        }
    }

    /**
     * Update status message
     * @param {string} message - Status message
     */
    updateStatus(message) {
        if (this.onStatusCallback) {
            this.onStatusCallback(message);
        }
    }

    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} Promise that resolves after delay
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
