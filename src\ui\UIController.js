/**
 * UIController.js - Handles all DOM interactions and UI updates
 * Manages the connection between the UI elements and the RubiksCubeApp
 */

export class UIController {
    constructor(app) {
        this.app = app;
        this.elements = {};
        this.isInitialized = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupUICallbacks();
        
        this.isInitialized = true;
    }

    /**
     * Initialize DOM element references
     */
    initializeElements() {
        // Control buttons
        this.elements.scrambleBtn = document.getElementById('scramble-btn');
        this.elements.resetBtn = document.getElementById('reset-btn');
        this.elements.wireframeBtn = document.getElementById('wireframe-btn');
        this.elements.executeFormulaBtn = document.getElementById('execute-formula');
        this.elements.formulaInput = document.getElementById('formula-input');

        // Manual rotation buttons
        this.elements.rotationBtns = document.querySelectorAll('.btn-rotation');

        // Camera controls
        this.elements.cameraFrontBtn = document.getElementById('camera-front');
        this.elements.cameraCornerBtn = document.getElementById('camera-corner');
        this.elements.cameraTopBtn = document.getElementById('camera-top');
        this.elements.cameraSideBtn = document.getElementById('camera-side');
        this.elements.zoomInBtn = document.getElementById('zoom-in');
        this.elements.zoomOutBtn = document.getElementById('zoom-out');
        this.elements.zoomResetBtn = document.getElementById('zoom-reset');

        // Data generation controls
        this.elements.captureStateBtn = document.getElementById('capture-state');
        this.elements.generateBatchBtn = document.getElementById('generate-batch');
        this.elements.exportDataBtn = document.getElementById('export-data');
        this.elements.batchSizeInput = document.getElementById('batch-size');
        this.elements.anglesPerStateInput = document.getElementById('angles-per-state');

        // Status and info elements
        this.elements.statusText = document.getElementById('status-text');
        this.elements.performanceInfo = document.getElementById('performance-info');
        this.elements.movesCount = document.getElementById('moves-count');
        this.elements.capturedCount = document.getElementById('captured-count');
        this.elements.currentCamera = document.getElementById('current-camera');
        this.elements.cameraPosition = document.getElementById('camera-position');

        // Validate required elements
        this.validateElements();
    }

    /**
     * Validate that all required DOM elements exist
     */
    validateElements() {
        const requiredElements = [
            'scrambleBtn', 'resetBtn', 'wireframeBtn', 'executeFormulaBtn', 'formulaInput',
            'statusText', 'performanceInfo', 'movesCount', 'capturedCount'
        ];

        const missingElements = requiredElements.filter(key => !this.elements[key]);
        
        if (missingElements.length > 0) {
            console.warn('Missing UI elements:', missingElements);
        }
    }

    /**
     * Setup event listeners for all UI elements
     */
    setupEventListeners() {
        // Cube control buttons
        if (this.elements.scrambleBtn) {
            this.elements.scrambleBtn.addEventListener('click', () => this.handleScramble());
        }
        
        if (this.elements.resetBtn) {
            this.elements.resetBtn.addEventListener('click', () => this.handleReset());
        }
        
        if (this.elements.wireframeBtn) {
            this.elements.wireframeBtn.addEventListener('click', () => this.handleToggleWireframe());
        }
        
        if (this.elements.executeFormulaBtn) {
            this.elements.executeFormulaBtn.addEventListener('click', () => this.handleExecuteFormula());
        }

        // Formula input enter key
        if (this.elements.formulaInput) {
            this.elements.formulaInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.handleExecuteFormula();
                }
            });
        }

        // Manual rotation buttons
        this.elements.rotationBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const move = e.target.dataset.move;
                if (move) {
                    this.handleMove(move);
                }
            });
        });

        // Camera preset buttons
        const cameraButtons = [
            { element: this.elements.cameraFrontBtn, preset: 'front' },
            { element: this.elements.cameraCornerBtn, preset: 'corner' },
            { element: this.elements.cameraTopBtn, preset: 'top' },
            { element: this.elements.cameraSideBtn, preset: 'side' }
        ];

        cameraButtons.forEach(({ element, preset }) => {
            if (element) {
                element.addEventListener('click', () => this.handleCameraPreset(preset));
            }
        });

        // Zoom controls
        if (this.elements.zoomInBtn) {
            this.elements.zoomInBtn.addEventListener('click', () => this.handleZoom('in'));
        }
        
        if (this.elements.zoomOutBtn) {
            this.elements.zoomOutBtn.addEventListener('click', () => this.handleZoom('out'));
        }
        
        if (this.elements.zoomResetBtn) {
            this.elements.zoomResetBtn.addEventListener('click', () => this.handleZoom('reset'));
        }

        // Data generation buttons
        if (this.elements.captureStateBtn) {
            this.elements.captureStateBtn.addEventListener('click', () => this.handleCaptureState());
        }
        
        if (this.elements.generateBatchBtn) {
            this.elements.generateBatchBtn.addEventListener('click', () => this.handleGenerateBatch());
        }
        
        if (this.elements.exportDataBtn) {
            this.elements.exportDataBtn.addEventListener('click', () => this.handleExportData());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Window resize
        window.addEventListener('resize', () => this.app.onWindowResize());
    }

    /**
     * Setup UI update callbacks with the app
     */
    setupUICallbacks() {
        this.app.setUICallbacks({
            onUIUpdate: (data) => this.updateUI(data),
            onStatusUpdate: (message) => this.updateStatus(message),
            onProgressUpdate: (progress) => this.updateProgress(progress),
            onCameraUpdate: (preset) => this.updateCamera(preset),
            onPerformanceUpdate: (fps) => this.updatePerformance(fps)
        });
    }

    /**
     * Handle scramble button click
     */
    async handleScramble() {
        if (this.app.isBusy()) return;
        
        this.setControlsEnabled(false);
        try {
            await this.app.scramble();
        } finally {
            this.setControlsEnabled(true);
        }
    }

    /**
     * Handle reset button click
     */
    handleReset() {
        if (this.app.isBusy()) return;
        this.app.reset();
    }

    /**
     * Handle wireframe toggle
     */
    handleToggleWireframe() {
        this.app.toggleWireframe();
    }

    /**
     * Handle formula execution
     */
    async handleExecuteFormula() {
        if (this.app.isBusy()) return;
        
        const formula = this.elements.formulaInput?.value?.trim();
        if (!formula) {
            alert('Please enter a formula (e.g., R U R\' U\')');
            return;
        }

        this.setControlsEnabled(false);
        try {
            const success = await this.app.executeFormula(formula);
            if (success && this.elements.formulaInput) {
                this.elements.formulaInput.value = ''; // Clear input after successful execution
            }
        } finally {
            this.setControlsEnabled(true);
        }
    }

    /**
     * Handle individual move execution
     * @param {string} move - Move notation
     */
    async handleMove(move) {
        if (this.app.isBusy()) return;
        
        this.setControlsEnabled(false);
        try {
            await this.app.executeMove(move);
        } finally {
            this.setControlsEnabled(true);
        }
    }

    /**
     * Handle camera preset selection
     * @param {string} preset - Preset name
     */
    async handleCameraPreset(preset) {
        if (this.app.isBusy()) return;
        await this.app.setCameraPreset(preset);
    }

    /**
     * Handle zoom controls
     * @param {string} direction - Zoom direction
     */
    handleZoom(direction) {
        this.app.zoom(direction);
    }

    /**
     * Handle state capture
     */
    handleCaptureState() {
        if (this.app.isBusy()) return;
        this.app.captureCurrentState();
    }

    /**
     * Handle batch generation
     */
    async handleGenerateBatch() {
        if (this.app.isBusy()) return;

        const batchSize = parseInt(this.elements.batchSizeInput?.value || '10');
        const anglesPerState = parseInt(this.elements.anglesPerStateInput?.value || '4');

        const options = {
            stateCount: batchSize,
            anglesPerState: anglesPerState
        };

        this.setControlsEnabled(false);
        try {
            await this.app.generateBatch(options);
        } finally {
            this.setControlsEnabled(true);
        }
    }

    /**
     * Handle data export
     */
    handleExportData() {
        if (this.app.getDataCount() === 0) {
            alert('No data to export. Capture some states first!');
            return;
        }

        try {
            this.app.exportData('json', { pretty: true });
        } catch (error) {
            alert(`Export failed: ${error.message}`);
        }
    }

    /**
     * Handle keyboard shortcuts
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyboard(e) {
        // Don't trigger shortcuts when typing in input fields
        if (e.target.tagName === 'INPUT') {
            return;
        }

        switch (e.key.toLowerCase()) {
            case 'w':
                this.handleToggleWireframe();
                break;
            case '+':
            case '=':
                this.handleZoom('in');
                break;
            case '-':
            case '_':
                this.handleZoom('out');
                break;
            case '0':
                this.handleZoom('reset');
                break;
        }
    }

    /**
     * Update UI elements with current app state
     * @param {Object} data - UI data object
     */
    updateUI(data) {
        if (this.elements.movesCount) {
            this.elements.movesCount.textContent = data.moveCount || 0;
        }
        
        if (this.elements.capturedCount) {
            this.elements.capturedCount.textContent = data.dataCount || 0;
        }

        // Update button states based on busy status
        this.setControlsEnabled(!data.isBusy);
    }

    /**
     * Update status message
     * @param {string} message - Status message
     */
    updateStatus(message) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = message;
        }
    }

    /**
     * Update progress (for batch generation)
     * @param {number} progress - Progress percentage
     */
    updateProgress(progress) {
        // Could add a progress bar here in the future
        console.log(`Progress: ${progress.toFixed(1)}%`);
    }

    /**
     * Update camera information
     * @param {string} preset - Camera preset name
     */
    updateCamera(preset) {
        if (this.elements.currentCamera) {
            this.elements.currentCamera.textContent = preset;
        }
        
        if (this.elements.cameraPosition) {
            this.elements.cameraPosition.textContent = `Camera: ${preset}`;
        }
    }

    /**
     * Update performance information
     * @param {number} fps - Frames per second
     */
    updatePerformance(fps) {
        if (this.elements.performanceInfo) {
            this.elements.performanceInfo.textContent = `FPS: ${fps}`;
        }
    }

    /**
     * Enable or disable controls
     * @param {boolean} enabled - Whether controls should be enabled
     */
    setControlsEnabled(enabled) {
        const buttons = document.querySelectorAll('.btn');
        const inputs = document.querySelectorAll('input');

        buttons.forEach(btn => btn.disabled = !enabled);
        inputs.forEach(input => input.disabled = !enabled);
    }
}
