/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
    flex: 1;
}

/* Canvas Container */
.canvas-container {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    min-height: 600px;
}

#cube-canvas {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 15px;
}

.canvas-overlay {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    pointer-events: none;
}

.camera-info {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    display: inline-block;
}

/* Control Panel */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    height: fit-content;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.control-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.control-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-section h3 {
    color: #2a5298;
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 3px;
    min-width: 60px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.btn-camera {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.btn-rotation {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    font-weight: 600;
    min-width: 45px;
}

/* Button Groups */
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.rotation-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rotation-row {
    display: flex;
    justify-content: space-between;
    gap: 5px;
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.input-group label {
    flex: 1;
    font-weight: 500;
    color: #555;
}

.input-group input {
    width: 80px;
    padding: 6px 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Data Controls */
.data-controls {
    display: flex;
    flex-direction: column;
}

/* State Info */
.state-info {
    background: rgba(42, 82, 152, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #2a5298;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .label {
    font-weight: 500;
    color: #555;
}

.info-item span:last-child {
    font-weight: 600;
    color: #2a5298;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    border-radius: 10px;
    margin-top: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-item {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .control-panel {
        order: -1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .rotation-row {
        justify-content: center;
    }
    
    .btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Disabled State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}
